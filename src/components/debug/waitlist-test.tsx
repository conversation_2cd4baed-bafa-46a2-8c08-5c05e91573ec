"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useWaitlist } from "@/components/providers/waitlist-provider";
import { usePostHogAnonymous } from "@/hooks/use-posthog-anonymous";

/**
 * Simple test component for waitlist functionality
 * Add this to any page to test waitlist modal and feature flags
 */
export function WaitlistTest() {
  const { showWaitlistModal, isWaitlistEnabled } = useWaitlist();
  const { isFeatureEnabled, getCurrentAnonymousId, isAnonymousTrackingReady } = usePostHogAnonymous();

  if (process.env.NODE_ENV === "production") {
    return null;
  }

  const waitlistFlagValue = isFeatureEnabled("enable-waitlist");

  return (
    <Card className="max-w-md mx-auto mt-8">
      <CardHeader>
        <CardTitle>Waitlist Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Status */}
        <div className="text-sm space-y-1">
          <div>Anonymous ID: {getCurrentAnonymousId()?.slice(0, 8)}...</div>
          <div>Tracking Ready: {isAnonymousTrackingReady ? "✅" : "❌"}</div>
          <div>Feature Flag: {waitlistFlagValue ? "✅" : "❌"}</div>
          <div>Waitlist Enabled: {isWaitlistEnabled ? "✅" : "❌"}</div>
        </div>

        {/* Test Buttons */}
        <div className="space-y-2">
          <Button 
            onClick={() => showWaitlistModal("homeowner")} 
            disabled={!isWaitlistEnabled}
            className="w-full"
          >
            Test Homeowner Waitlist
          </Button>
          
          <Button 
            onClick={() => showWaitlistModal("contractor")} 
            disabled={!isWaitlistEnabled}
            variant="outline"
            className="w-full"
          >
            Test Contractor Waitlist
          </Button>
        </div>

        {/* Test Links */}
        <div className="space-y-2">
          <div className="text-sm font-medium">Test Sign-up Links:</div>
          <div className="space-y-1">
            <a 
              href="/sign-up/homeowner" 
              className="block text-blue-600 hover:underline text-sm"
            >
              /sign-up/homeowner
            </a>
            <a 
              href="/sign-up/contractor" 
              className="block text-blue-600 hover:underline text-sm"
            >
              /sign-up/contractor
            </a>
            <a 
              href="/sign-up" 
              className="block text-blue-600 hover:underline text-sm"
            >
              /sign-up (generic)
            </a>
          </div>
        </div>

        {/* Debug Info */}
        {!isWaitlistEnabled && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded text-sm">
            <strong>Waitlist Disabled</strong>
            <div className="mt-1">
              The waitlist feature flag "enable-waitlist" is not enabled for this user.
              Check PostHog feature flag configuration.
            </div>
          </div>
        )}

        {!isAnonymousTrackingReady && (
          <div className="p-3 bg-red-50 border border-red-200 rounded text-sm">
            <strong>Tracking Not Ready</strong>
            <div className="mt-1">
              Anonymous tracking is not ready. Check PostHog configuration and anonymous ID setup.
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Minimal waitlist status indicator
 */
export function WaitlistStatus() {
  const { isWaitlistEnabled } = useWaitlist();
  const { isAnonymousTrackingReady } = usePostHogAnonymous();

  if (process.env.NODE_ENV === "production") {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 bg-background/90 backdrop-blur p-2 rounded text-xs border">
      <div>Waitlist: {isWaitlistEnabled ? "✅" : "❌"}</div>
      <div>Tracking: {isAnonymousTrackingReady ? "✅" : "❌"}</div>
    </div>
  );
}
