"use client";

import { useState } from "react";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  usePostHogAnonymous,
  type WaitlistSignupProperties,
} from "@/hooks/use-posthog-anonymous";

/**
 * Example component demonstrating how to use PostHog anonymous tracking
 * for waitlist signups with proper identification and event tracking
 *
 * NOTE: This is now integrated into the WaitlistProvider and WaitlistModal
 * This example shows how the tracking works under the hood
 */
export function WaitlistSignupExample() {
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    trackWaitlistSignup,
    captureAnonymous,
    isAnonymousTrackingReady,
    getCurrentAnonymousId,
  } = usePostHogAnonymous();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !isAnonymousTrackingReady) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare additional properties for the waitlist signup
      const signupProperties: WaitlistSignupProperties = {
        email,
        signup_source: "landing_page",
        landing_page: window.location.pathname,
        referrer: document.referrer || undefined,
        user_agent: navigator.userAgent,
        timestamp: new Date().toISOString(),
      };

      // Track the waitlist signup with PostHog
      trackWaitlistSignup(email, signupProperties);

      // You can also capture additional events
      captureAnonymous("waitlist_form_submitted", {
        email,
        form_location: "hero_section",
      });

      // Here you would typically also submit to your backend
      // await submitToWaitlist(email, signupProperties);

      toast.success("Success!", {
        description:
          "You've been added to our waitlist. We'll be in touch soon!",
      });

      setEmail("");
    } catch (error) {
      console.error("Error submitting waitlist signup:", error);

      // Track the error
      captureAnonymous("waitlist_signup_error", {
        email,
        error: error instanceof Error ? error.message : "Unknown error",
      });

      toast.error("Error", {
        description: "Something went wrong. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Track form interactions
  const handleEmailFocus = () => {
    captureAnonymous("waitlist_email_focused", {
      form_location: "hero_section",
    });
  };

  const handleEmailChange = (value: string) => {
    setEmail(value);

    // Track when user starts typing (only once per session)
    if (value.length === 1) {
      captureAnonymous("waitlist_email_started_typing", {
        form_location: "hero_section",
      });
    }
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-card rounded-lg border">
      <div className="space-y-4">
        <div className="text-center">
          <h2 className="text-2xl font-bold">Join Our Waitlist</h2>
          <p className="text-muted-foreground">
            Be the first to know when we launch!
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => handleEmailChange(e.target.value)}
              onFocus={handleEmailFocus}
              required
              disabled={isSubmitting || !isAnonymousTrackingReady}
            />
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={isSubmitting || !email || !isAnonymousTrackingReady}
          >
            {isSubmitting ? "Joining..." : "Join Waitlist"}
          </Button>
        </form>

        {/* Debug info (remove in production) */}
        {process.env.NODE_ENV === "development" && (
          <div className="mt-4 p-3 bg-muted rounded text-sm">
            <p>
              <strong>Debug Info:</strong>
            </p>
            <p>Anonymous ID: {getCurrentAnonymousId()}</p>
            <p>Tracking Ready: {isAnonymousTrackingReady ? "Yes" : "No"}</p>
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * Example of a simpler waitlist button component
 */
export function WaitlistButtonExample() {
  const { captureAnonymous, isAnonymousTrackingReady } = usePostHogAnonymous();

  const handleClick = () => {
    // Track interest in joining waitlist
    captureAnonymous("waitlist_interest_clicked", {
      button_location: "navigation",
      page: window.location.pathname,
    });

    // Here you might open a modal or navigate to signup page
    console.log("Opening waitlist signup...");
  };

  return (
    <Button
      onClick={handleClick}
      disabled={!isAnonymousTrackingReady}
      variant="outline"
    >
      Join Waitlist
    </Button>
  );
}

/**
 * Example of tracking feature interest for anonymous users
 */
export function FeatureInterestExample() {
  const { captureAnonymous, getFeatureFlag, isFeatureEnabled } =
    usePostHogAnonymous();

  const handleFeatureClick = (featureName: string) => {
    captureAnonymous("feature_interest_clicked", {
      feature_name: featureName,
      page: window.location.pathname,
    });
  };

  // Example of using feature flags for anonymous users
  const showBetaFeatures = isFeatureEnabled("show_beta_features_anonymous");
  const ctaVariant = getFeatureFlag("cta_variant", "default");

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Features You'll Love</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div
          className="p-4 border rounded cursor-pointer hover:bg-muted"
          onClick={() => handleFeatureClick("project_management")}
        >
          <h4 className="font-medium">Project Management</h4>
          <p className="text-sm text-muted-foreground">
            Organize your projects efficiently
          </p>
        </div>

        <div
          className="p-4 border rounded cursor-pointer hover:bg-muted"
          onClick={() => handleFeatureClick("team_collaboration")}
        >
          <h4 className="font-medium">Team Collaboration</h4>
          <p className="text-sm text-muted-foreground">
            Work together seamlessly
          </p>
        </div>

        {showBetaFeatures && (
          <div
            className="p-4 border rounded cursor-pointer hover:bg-muted border-orange-200"
            onClick={() => handleFeatureClick("ai_assistant")}
          >
            <h4 className="font-medium">AI Assistant (Beta)</h4>
            <p className="text-sm text-muted-foreground">
              Get help from our AI assistant
            </p>
          </div>
        )}
      </div>

      <Button
        onClick={() => handleFeatureClick("all_features")}
        variant={ctaVariant === "primary" ? "default" : "outline"}
        className="w-full"
      >
        {ctaVariant === "primary" ? "Get Started Now" : "Learn More"}
      </Button>
    </div>
  );
}
